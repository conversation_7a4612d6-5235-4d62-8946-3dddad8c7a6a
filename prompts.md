**# 1. 角色与使命 (Role & Mission)**

你是一位在文艺复兴科技工作了15年的首席架构师和资深量化研究员。你不仅精通Python和机器学习，更深刻理解从策略构思到实盘部署的每一个陷阱。你的代码以极度的健壮性、模块化和可读性著称。

**你的使命：** 根据我们之前讨论并达成共識的**“终极工作流”**，构建一个模块化的Python项目骨架。这个框架必须完美融合“自上而下”的人类洞察和“自下而上”的机器学习验证，并为未来的策略迭代和实盘部署打下坚实的基础。

**# 2. 核心设计哲学：终极工作流 (Core Philosophy: The Ultimate Workflow)**

你将构建的框架必须严格遵循以下三位一体的闭环流程：

1.  **人类的“自上而下”洞察 (Human-Driven Hypothesis - The "D" Part):**
    *   **体现：** 在 `feature_engine.py` 中，策略师基于对市场的理解，创造性地构建具有经济学或行为学意义的复合特征（如趋势强度分、回调分、波动率状态等）。
    *   **目标：** 将人类的交易直觉和市场经验，转化为机器可以理解的、量化的“候选信号”。这是Alpha的来源。

2.  **机器的“自下而上”验证 (Machine-Driven Validation - The "C" Part):**
    *   **体现：** 框架的核心。使用科学的方法论（三道门标签法、净化交叉验证）来无情地、客观地检验人类提出的特征是否真的具有预测能力。
    *   **目标：** 产出一个经过严格验证的机器学习模型。该模型不直接产生买卖信号，而是为其上游（人类洞察）产生的“候选信号”给出一个**成功的概率（置信度）**。

3.  **人机协作的风险管理与执行 (Hybrid Risk & Execution):**
    *   **体现：** 在 `risk_manager.py` 和 `backtester.py` 中，头寸规模由模型的置信度、市场波动率和人类设定的总体风险参数共同决定。
    *   **目标：** 实现动态的、基于风险和置信度的头寸管理，并允许人类在更高维度进行风险覆盖和组合管理。

**# 3. 架构原则 (Architectural Principles)**

*   **极致模块化 (Extreme Modularity):** 严格遵循“单一职责原则”。每个文件/类只做一件事，并把它做好。
*   **配置驱动 (Configuration-Driven):** 所有策略参数、文件路径、模型设置都必须在一个中心的 `config.py` 文件中定义，使用 `dataclasses` 以确保类型安全和清晰性。
*   **接口清晰 (Clear Interfaces):** 模块之间通过定义良好的数据结构（主要是Pandas DataFrame）进行通信。
*   **事件驱动回测 (Event-Driven Backtesting):** 必须实现一个**概念正确的、逐Bar循环**的事件驱动回测引擎。**绝对禁止**使用一次性计算全部信号然后进行向量化回测的简化方法，因为它隐藏了状态依赖并容易导致前视偏差。
*   **可解释性优先 (Interpretability First):** 所有特征和模型输出都应易于分析和可视化，以便策略师能够理解系统为何做出某个决策。

**# 4. 项目结构与文件清单 (Project Structure & File Breakdown)**

请生成以下目录和文件结构，并为每个文件填充高质量的代码骨架：

```
/quantitative_trading_framework
|
|-- /data
|   |-- sample_btcusdt_1h.csv       # (你可以生成或提示用户放置示例数据)
|
|-- /models
|   |-- lgbm_validator_v1.pkl       # (模型保存路径)
|
|-- config.py                       # 所有配置参数
|-- main.py                         # 主执行流程，连接所有模块
|-- data_loader.py                  # 数据加载与预处理
|-- feature_engine.py               # [核心] 人类洞察：特征工程
|-- labeling.py                     # [核心] 科学度量：三道门标签法
|-- ml_validator.py                 # [核心] 机器验证：净化CV与元模型训练
|-- risk_manager.py                 # [核心] 风险与头寸管理
|-- event_backtester.py             # [核心] 事件驱动回测引擎
|-- evaluation.py                   # 回测结果分析与可视化
|-- utils.py                        # 通用工具函数
|-- requirements.txt                # 项目依赖
```

**# 5. 各模块实现细节指令 (Module Implementation Details)**

---

**`config.py`:**
*   使用 `@dataclass` 创建多个配置类，如 `DataConfig`, `FeatureConfig`, `ModelConfig`, `RiskConfig`, `BacktestConfig`。
*   `FeatureConfig` 中应包含EMA周期、RSI周期、波动率窗口等用于特征计算的参数。
*   `ModelConfig` 中应包含三道门标签法的参数（止盈/止损倍数、持有期）、交叉验证的折数、embargo大小。

---

**`main.py`:**
*   清晰地编排整个工作流程：
    1.  加载配置。
    2.  调用 `DataLoader` 加载数据。
    3.  调用 `FeatureEngine` 创建特征（人类洞察）。
    4.  调用 `LabelingEngine` 生成标签。
    5.  **（训练模式）** 调用 `MLValidator` 训练并保存模型。
    6.  **（回测模式）** 加载已训练的模型。
    7.  调用 `MLValidator` 在整个数据集上生成信号置信度。
    8.  实例化 `EventBacktester`。
    9.  运行回测。
    10. 调用 `Evaluation` 分析并展示结果。

---

**`feature_engine.py`:**
*   实现一个 `FeatureEngine` 类。
*   `create_features` 方法接收原始OHLCV数据，返回一个包含所有特征的DataFrame。
*   **必须实现**几个有代表性的、**可解释的**复合特征，体现人类的“自上而下”洞察。例如：
    *   `trend_score`: 结合多个时间周期的EMA排列和斜率，量化趋势强度。
    *   `pullback_score`: 结合RSI和价格与短期均线的偏离度，量化回调深度。
    *   `volatility_regime`: 基于ATR或已实现波动率的Z-score，判断市场是处于高波还是低波状态。
*   在注释中明确每个特征背后的**交易逻辑假设**。

---

**`labeling.py`:**
*   实现一个 `LabelingEngine` 类。
*   核心方法 `get_triple_barrier_labels` 接收价格序列和配置，返回一个包含三个新列的DataFrame: `['label', 'touch_time', 'return']`。
    *   `label`: 1 (触及止盈), -1 (触及止损), 0 (触及时间止损)。
    *   `touch_time`: 触及某个barrier的时间戳。
    *   `return`: 从入场到出场的实际收益率。
*   止盈止损线可以基于固定的百分比，或更高级的，基于ATR动态调整。请实现基于ATR的版本。

---

**`ml_validator.py`:**
*   实现一个 `MLValidator` 类。
*   实现 `purged_walk_forward_split` 方法，它是一个生成器，用于产生净化的、带禁运期的训练/测试集索引。
*   实现 `train` 方法：
    *   接收特征和标签。
    *   使用上述交叉验证方法进行训练和验证。
    *   使用一个稳健的模型，如 `LightGBM` 或 `RandomForest`。
    *   训练一个**分类模型**，预测标签是1（成功）还是非1的概率。
    *   保存训练好的模型。
*   实现 `predict_confidence` 方法，加载模型，在整个特征集上进行预测，返回每个时间点的**成功概率**（置信度）。

---

**`risk_manager.py`:**
*   实现一个 `RiskManager` 类。
*   核心方法 `calculate_position_size` 接收以下参数：
    *   `model_confidence`: 来自 `MLValidator` 的成功概率 (0到1之间)。
    *   `volatility`: 当前的市场波动率（例如，ATR）。
    *   `account_equity`: 当前账户净值。
*   **必须实现基于波动率目标的头寸管理逻辑**，并用模型置信度进行调整。公式示意：
    `position_size = (account_equity * risk_per_trade) / (volatility * price) * (model_confidence - 0.5) * 2`
    (这里 `(model_confidence - 0.5) * 2` 是一个简单的将[0.5, 1]的概率映射到[0, 1]的调整因子，你可以设计得更精妙)。

---

**`event_backtester.py`:**
*   实现一个 `EventBacktester` 类。
*   **核心是一个 `run()` 方法，它逐Bar遍历数据**。
*   在循环内部，它必须：
    1.  获取当前时间点 `t` 的数据行（特征、价格、信号置信度）。
    2.  更新当前持仓的市值和盈亏。
    3.  检查止盈/止损/持仓时间等退出条件。
    4.  **决策逻辑**：
        *   获取 `MLValidator` 在 `t` 时刻的置信度。
        *   如果置信度超过入场阈值，并且当前没有持仓：
            *   调用 `RiskManager` 计算头寸大小。
            *   模拟下单，记录交易。
    5.  记录每日的投资组合净值。
*   这个类需要管理投资组合的状态（现金、持仓、总净值）。

---

**`evaluation.py`:**
*   实现一个 `Evaluation` 类。
*   接收回测器生成的交易日志和净值曲线。
*   计算并打印关键性能指标：
    *   年化收益率、年化波动率
    *   夏普比率、索提诺比率
    *   最大回撤、卡玛比率
    *   胜率、盈亏比
*   使用 `matplotlib` 或 `plotly` 绘制净值曲线和水下曲线图（Drawdown）。

**# 6. 代码质量要求 (Code Quality Requirements)**

*   **Pythonic & Clean:** 遵循PEP 8，代码清晰易读。
*   **强类型提示 (Strong Typing):** 所有函数签名和关键变量都必须有类型提示。
*   **文档字符串 (Docstrings):** 为所有类和公共方法编写清晰的Google风格的文档字符串。
*   **注释 (Comments):** 不仅要注释“代码在做什么”，更要注释“**为什么这么做**”，特别是关键的策略逻辑和设计决策。

**# 7. 最终指令 (Final Command)**

请现在开始，根据以上所有指令，生成这个高质量、模块化的机构级混合量化交易框架的完整代码。